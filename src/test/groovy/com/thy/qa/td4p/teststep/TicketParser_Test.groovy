package com.thy.qa.td4p.teststep

import org.junit.Test

class TicketParser_Test implements TicketParser {

    @Test
    void parseTicketNumbers_multipleTickets_shouldReturnAllTicketNumbers() {
        String responseContent = new File(this.class.getResource('/response/PurchaseBasketResponse_MultipleTickets.xml').toURI()).text
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.size() == 3
        assert ticketNumbers.contains('2351234567891')
        assert ticketNumbers.contains('2351234567892')
        assert ticketNumbers.contains('2351234567893')
        
        // Verify the order is preserved as they appear in the XML
        assert ticketNumbers[0] == '2351234567891'
        assert ticketNumbers[1] == '2351234567892'
        assert ticketNumbers[2] == '2351234567893'
    }

    @Test
    void parseTicketNumbers_singleTicket_shouldReturnSingleTicketNumber() {
        String responseContent = new File(this.class.getResource('/response/FinalizeTicketResponse_SingleTicket.xml').toURI()).text
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.size() == 1
        assert ticketNumbers[0] == '2351234567890'
    }

    @Test
    void parseTicketNumbers_emptyResponse_shouldReturnEmptyList() {
        String responseContent = new File(this.class.getResource('/response/EmptyTicketResponse.xml').toURI()).text
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.isEmpty()
    }

    @Test
    void parseTicketNumbers_noTicketingElements_shouldReturnEmptyList() {
        String responseContent = '''<?xml version="1.0" encoding="UTF-8"?>
<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <S:Body>
        <ns0:createTicketResponse xmlns:ns0="http://service.thy.com/">
            <ns0:createTicketOTAResponse>
                <DemandTicketDetail>
                    <BookingReferenceID Type="PNR" ID="ABC123"/>
                    <!-- No Ticketing elements -->
                </DemandTicketDetail>
            </ns0:createTicketOTAResponse>
        </ns0:createTicketResponse>
    </S:Body>
</S:Envelope>'''
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.isEmpty()
    }

    @Test
    void parseTicketNumbers_malformedXml_shouldHandleGracefully() {
        String responseContent = '''<?xml version="1.0" encoding="UTF-8"?>
<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <S:Body>
        <ns0:createTicketResponse xmlns:ns0="http://service.thy.com/">
            <ns0:createTicketOTAResponse>
                <DemandTicketDetail>
                    <BookingReferenceID Type="PNR" ID="ABC123"/>
                    <Ticketing TicketDocumentNbr="2351111111111" TicketingStatus="OK">
                        <!-- Incomplete ticket element -->
                    </Ticketing>
                </DemandTicketDetail>
            </ns0:createTicketOTAResponse>
        </ns0:createTicketResponse>
    </S:Body>
</S:Envelope>'''
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.size() == 1
        assert ticketNumbers[0] == '2351111111111'
    }

    @Test
    void parseTicketNumbers_mixedNamespaces_shouldParseCorrectly() {
        String responseContent = '''<?xml version="1.0" encoding="UTF-8"?>
<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns2="http://www.opentravel.org/OTA/2003/05">
    <S:Body>
        <ns0:createTicketResponse xmlns:ns0="http://service.thy.com/">
            <ns0:createTicketOTAResponse>
                <ns2:OTA_AirDemandTicketRS>
                    <ns2:DemandTicketDetail>
                        <ns2:BookingReferenceID Type="PNR" ID="TEST123"/>
                        <ns2:Ticketing TicketDocumentNbr="2352222222222" TicketingStatus="OK"/>
                        <Ticketing TicketDocumentNbr="2353333333333" TicketingStatus="OK"/>
                    </ns2:DemandTicketDetail>
                </ns2:OTA_AirDemandTicketRS>
            </ns0:createTicketOTAResponse>
        </ns0:createTicketResponse>
    </S:Body>
</S:Envelope>'''
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.size() == 2
        assert ticketNumbers.contains('2352222222222')
        assert ticketNumbers.contains('2353333333333')
    }

    @Test
    void parseTicketNumbers_duplicateTicketNumbers_shouldReturnAllOccurrences() {
        String responseContent = '''<?xml version="1.0" encoding="UTF-8"?>
<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <S:Body>
        <ns0:createTicketResponse xmlns:ns0="http://service.thy.com/">
            <ns0:createTicketOTAResponse>
                <DemandTicketDetail>
                    <BookingReferenceID Type="PNR" ID="DUP123"/>
                    <Ticketing TicketDocumentNbr="2354444444444" TicketingStatus="OK"/>
                    <Ticketing TicketDocumentNbr="2354444444444" TicketingStatus="REISSUED"/>
                </DemandTicketDetail>
            </ns0:createTicketOTAResponse>
        </ns0:createTicketResponse>
    </S:Body>
</S:Envelope>'''
        
        List<String> ticketNumbers = parseTicketNumbers(responseContent)
        
        assert ticketNumbers.size() == 2
        assert ticketNumbers[0] == '2354444444444'
        assert ticketNumbers[1] == '2354444444444'
    }
}
